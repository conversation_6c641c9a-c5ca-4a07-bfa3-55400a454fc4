# MQTT协议支持设置指南

本文档介绍如何在AiVideoCore中设置和使用MQTT协议功能。

## 概述

AiVideoCore现在支持通过MQTT协议发布视频处理结果。MQTT是一种轻量级的消息传输协议，特别适合物联网应用。

## 依赖安装

### 1. 安装MQTT服务器

#### Windows
1. 下载并安装 [Eclipse Mosquitto](https://mosquitto.org/download/)
2. 或者使用Docker: `docker run -it -p 1883:1883 eclipse-mosquitto`

#### Linux (Ubuntu/Debian)
```bash
sudo apt-get update
sudo apt-get install mosquitto mosquitto-clients
sudo systemctl start mosquitto
sudo systemctl enable mosquitto
```

#### macOS
```bash
brew install mosquitto
brew services start mosquitto
```

### 2. Paho MQTT C++库（自动下载）

**好消息！** 现在您不需要手动安装Paho MQTT C++库了。项目已经配置为使用CMake的FetchContent功能自动下载和编译MQTT库。

#### 自动下载的优势
- **无需手动安装**: CMake会自动下载和编译所需的MQTT库
- **版本一致性**: 确保所有开发者使用相同版本的MQTT库
- **简化构建**: 只需要有网络连接即可自动获取依赖

#### 手动安装（可选）
如果您希望使用系统已安装的MQTT库，可以手动安装：

##### Windows (使用vcpkg)
```bash
vcpkg install paho-mqttpp3
```

##### Linux (Ubuntu/Debian)
```bash
# 安装依赖
sudo apt-get install build-essential cmake libssl-dev

# 安装Paho MQTT C库
git clone https://github.com/eclipse/paho.mqtt.c.git
cd paho.mqtt.c
cmake -Bbuild -H. -DPAHO_ENABLE_TESTING=OFF -DPAHO_BUILD_STATIC=ON
cmake --build build/ --target install
cd ..

# 安装Paho MQTT C++库
git clone https://github.com/eclipse/paho.mqtt.cpp.git
cd paho.mqtt.cpp
cmake -Bbuild -H. -DPAHO_BUILD_STATIC=ON
cmake --build build/ --target install
```

##### macOS
```bash
brew install paho-mqtt-cpp
```

**注意**: 如果系统中已安装MQTT库，CMake会优先使用已安装的版本。

## 编译配置

### 自动配置

项目现在使用FetchContent自动处理MQTT依赖，您无需手动配置。CMake会：

1. **首先尝试查找系统已安装的MQTT库**
2. **如果未找到，自动下载和编译Paho MQTT C++库**
3. **自动配置编译选项和链接设置**

### 编译

```bash
mkdir build
cd build
cmake ..
cmake --build . --config Release
```

### 编译输出信息

编译时您会看到以下信息之一：

#### 成功情况
```
-- Found installed Paho MQTT C++ library
-- MQTT support will be enabled
-- MQTT support enabled for AiVideoCore
```

或者

```
-- Paho MQTT C++ library not found, downloading via FetchContent...
-- Fetching Paho MQTT C library...
-- Fetching Paho MQTT C++ library...
-- Successfully built Paho MQTT C++ library via FetchContent
-- MQTT support will be enabled
-- MQTT support enabled for AiVideoCore
```

#### 失败情况
```
-- Failed to build Paho MQTT C++ library
-- MQTT support will be disabled
-- MQTT support disabled for AiVideoCore
```

### 网络要求

由于使用FetchContent自动下载，首次编译时需要网络连接来下载MQTT库源码。后续编译会使用缓存的源码。

## 使用方法

### 1. 基本使用

```cpp
#include "core/video_processing_core.h"
#include "core/protocols/result_protocol.h"

// 创建处理器
core::VideoProcessingCore processor;

// 启动MQTT结果存储服务
bool success = processor.start_result_storage_server(
    "results",                                          // 存储路径
    core::VideoResultStorageServer::StorageMode::IMMEDIATE,
    1883,                                              // MQTT端口
    5000,                                              // 刷新间隔
    core::protocols::ProtocolType::MQTT                // 协议类型
);

// 配置MQTT参数
auto storage_server = processor.get_result_storage_server();
if (storage_server) {
    storage_server->set_mqtt_server_address("tcp://localhost:1883");
    storage_server->set_mqtt_topic("aivideo/results");
    storage_server->set_mqtt_client_id("aivideo_client");
    storage_server->set_mqtt_qos(1);
}
```

### 2. 高级配置

```cpp
// 设置认证信息
storage_server->set_mqtt_credentials("username", "password");

// 设置自定义服务器地址
storage_server->set_mqtt_server_address("tcp://*************:1883");

// 设置自定义主题
storage_server->set_mqtt_topic("factory/line1/detection_results");

// 设置QoS级别
storage_server->set_mqtt_qos(2);  // Exactly once delivery
```

### 3. QoS级别说明

- **QoS 0**: At most once - 最多一次传递，可能丢失消息
- **QoS 1**: At least once - 至少一次传递，可能重复
- **QoS 2**: Exactly once - 恰好一次传递，最可靠但最慢

## 测试MQTT功能

### 1. 启动MQTT服务器
```bash
mosquitto -p 1883
```

### 2. 订阅消息
在另一个终端中：
```bash
mosquitto_sub -h localhost -p 1883 -t "aivideo/results"
```

### 3. 运行示例
```bash
./mqtt_example
```

您应该能在订阅终端中看到JSON格式的检测结果。

## 消息格式

MQTT消息采用JSON格式，包含以下字段：

```json
{
    "frame_id": 123,
    "timestamp": 1640995200000,
    "detections": [
        {
            "class_name": "person",
            "confidence": 0.85,
            "bbox": {
                "x": 100,
                "y": 100,
                "width": 80,
                "height": 120
            }
        }
    ],
    "tracking_results": [],
    "plugin_results": {}
}
```

## 故障排除

### 1. 编译错误
- **错误**: "Paho MQTT C++ library not found"
- **解决**: 确保正确安装了Paho MQTT C++库，并且CMake能找到它

### 2. 运行时错误
- **错误**: "MQTT协议未启用"
- **解决**: 重新编译项目，确保MQTT_ENABLED宏被定义

### 3. 连接失败
- **错误**: "连接到MQTT服务器失败"
- **解决**: 
  - 检查MQTT服务器是否正在运行
  - 验证服务器地址和端口是否正确
  - 检查防火墙设置

### 4. 消息未收到
- **检查**: 
  - MQTT主题是否正确
  - 客户端是否正确订阅了主题
  - QoS设置是否合适

## 性能考虑

1. **QoS级别**: 较高的QoS级别提供更好的可靠性，但会增加延迟
2. **消息频率**: 高频率的消息发送可能会影响性能
3. **网络延迟**: MQTT是网络协议，会受到网络条件影响

## 安全考虑

1. **认证**: 在生产环境中使用用户名和密码认证
2. **加密**: 考虑使用TLS加密 (mqtts://)
3. **访问控制**: 配置MQTT服务器的访问控制列表

## 示例代码

完整的示例代码请参考 `examples/mqtt_example.cpp`。
