#include "core/protocols/mqtt_result_protocol.h"
#include "utils/log_manager.h"
#include <chrono>
#include <sstream>

#include <mqtt/async_client.h>
#include <mqtt/connect_options.h>
#include <mqtt/message.h>
namespace core {
namespace protocols {

// MQTT客户端包装类
class MqttClientWrapper {
public:
    std::unique_ptr<mqtt::async_client> client;
    std::unique_ptr<mqtt::connect_options> conn_opts;
    
    MqttClientWrapper(const std::string& server_address, const std::string& client_id)
        : client(std::make_unique<mqtt::async_client>(server_address, client_id)),
          conn_opts(std::make_unique<mqtt::connect_options>()) {
    }
};

MqttResultProtocol::MqttResultProtocol()
    : running_(false),
      client_count_(0),
      port_(1883),
      server_address_("tcp://localhost:1883"),
      topic_("aivideo/results"),
      client_id_("aivideo_client"),
      username_(""),
      password_(""),
      qos_(1),
      mqtt_client_(nullptr) {
}

MqttResultProtocol::~MqttResultProtocol() {
    stop();
}

bool MqttResultProtocol::start(int port) {
    if (running_) {
        return true; // 已经在运行
    }
    port_ = port;
    
    // 如果server_address_中没有指定端口，则使用传入的端口
    if (server_address_.find(":") == std::string::npos || 
        server_address_.find(":" + std::to_string(port_)) == std::string::npos) {
        // 更新服务器地址中的端口
        size_t protocol_end = server_address_.find("://");
        if (protocol_end != std::string::npos) {
            std::string protocol = server_address_.substr(0, protocol_end + 3);
            size_t host_start = protocol_end + 3;
            size_t port_start = server_address_.find(":", host_start);
            std::string host;
            if (port_start != std::string::npos) {
                host = server_address_.substr(host_start, port_start - host_start);
            } else {
                host = server_address_.substr(host_start);
            }
            server_address_ = protocol + host + ":" + std::to_string(port_);
        }
    }

    if (!initialize_mqtt_client()) {
        LOG_ERROR("初始化MQTT客户端失败");
        return false;
    }

    running_ = true;
    mqtt_thread_ = std::thread(&MqttResultProtocol::mqtt_thread_func, this);

    LOG_INFO("MQTT协议服务已启动，服务器: " + server_address_ + ", 主题: " + topic_);
    return true;
}

void MqttResultProtocol::stop() {
    if (!running_) {
        return;
    }

    running_ = false;

    // 断开MQTT连接
    disconnect_from_server();

    // 等待MQTT线程结束
    if (mqtt_thread_.joinable()) {
        mqtt_thread_.join();
    }

    // 清理MQTT客户端
    cleanup_mqtt_client();

    client_count_ = 0;
    LOG_INFO("MQTT协议服务已停止");
}

bool MqttResultProtocol::send_result(const std::string& json_str) {
    if (!running_) {
        return false;
    }

    if (!mqtt_client_) {
        return false;
    }

    try {
        MqttClientWrapper* wrapper = static_cast<MqttClientWrapper*>(mqtt_client_);
        if (!wrapper->client || !wrapper->client->is_connected()) {
            LOG_WARNING("MQTT客户端未连接，无法发送消息");
            return false;
        }

        // 创建消息
        auto msg = mqtt::make_message(topic_, json_str);
        msg->set_qos(qos_);

        // 发送消息
        wrapper->client->publish(msg);
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("发送MQTT消息失败: " + std::string(e.what()));
        return false;
    }
}

int MqttResultProtocol::get_client_count() const {
    return client_count_;
}

int MqttResultProtocol::get_port() const {
    return port_;
}

std::string MqttResultProtocol::get_protocol_name() const {
    return "MQTT";
}

bool MqttResultProtocol::is_running() const {
    return running_;
}

void MqttResultProtocol::set_new_connection_callback(std::function<void(void*)> callback) {
    new_connection_callback_ = callback;
}

void MqttResultProtocol::set_server_address(const std::string& server_address) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    server_address_ = server_address;
}

void MqttResultProtocol::set_topic(const std::string& topic) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    topic_ = topic;
}

void MqttResultProtocol::set_client_id(const std::string& client_id) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    client_id_ = client_id;
}

void MqttResultProtocol::set_credentials(const std::string& username, const std::string& password) {
    std::lock_guard<std::mutex> lock(config_mutex_);
    username_ = username;
    password_ = password;
}

void MqttResultProtocol::set_qos(int qos) {
    if (qos >= 0 && qos <= 2) {
        std::lock_guard<std::mutex> lock(config_mutex_);
        qos_ = qos;
    }
}

std::string MqttResultProtocol::get_server_address() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return server_address_;
}

std::string MqttResultProtocol::get_topic() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return topic_;
}

std::string MqttResultProtocol::get_client_id() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return client_id_;
}

int MqttResultProtocol::get_qos() const {
    std::lock_guard<std::mutex> lock(config_mutex_);
    return qos_;
}

bool MqttResultProtocol::initialize_mqtt_client() {
    try {
        // 创建MQTT客户端包装器
        auto wrapper = std::make_unique<MqttClientWrapper>(server_address_, client_id_);
        
        // 设置连接选项
        wrapper->conn_opts->set_keep_alive_interval(20);
        wrapper->conn_opts->set_clean_session(true);
        wrapper->conn_opts->set_automatic_reconnect(true);
        
        // 设置用户名和密码（如果提供）
        if (!username_.empty()) {
            wrapper->conn_opts->set_user_name(username_);
            if (!password_.empty()) {
                wrapper->conn_opts->set_password(password_);
            }
        }

        mqtt_client_ = wrapper.release();
        return true;
    } catch (const std::exception& e) {
        LOG_ERROR("初始化MQTT客户端失败: " + std::string(e.what()));
        return false;
    }
    return false;
}

void MqttResultProtocol::cleanup_mqtt_client() {
    if (mqtt_client_) {
        delete static_cast<MqttClientWrapper*>(mqtt_client_);
        mqtt_client_ = nullptr;
    }
}

bool MqttResultProtocol::connect_to_server() {
    if (!mqtt_client_) {
        return false;
    }

    try {
        MqttClientWrapper* wrapper = static_cast<MqttClientWrapper*>(mqtt_client_);
        
        LOG_INFO("正在连接到MQTT服务器: " + server_address_);
        auto token = wrapper->client->connect(*wrapper->conn_opts);
        token->wait();
        
        if (wrapper->client->is_connected()) {
            LOG_INFO("成功连接到MQTT服务器");
            client_count_ = 1; // MQTT是发布者，设置为1表示已连接
            
            // 触发新连接回调
            if (new_connection_callback_) {
                new_connection_callback_(wrapper->client.get());
            }
            
            return true;
        } else {
            LOG_ERROR("连接到MQTT服务器失败");
            return false;
        }
    } catch (const std::exception& e) {
        LOG_ERROR("连接MQTT服务器时发生异常: " + std::string(e.what()));
        return false;
    }
}

void MqttResultProtocol::disconnect_from_server() {
    if (!mqtt_client_) {
        return;
    }

    try {
        MqttClientWrapper* wrapper = static_cast<MqttClientWrapper*>(mqtt_client_);
        if (wrapper->client && wrapper->client->is_connected()) {
            LOG_INFO("正在断开MQTT连接");
            auto token = wrapper->client->disconnect();
            token->wait();
            LOG_INFO("MQTT连接已断开");
        }
        client_count_ = 0;
    } catch (const std::exception& e) {
        LOG_ERROR("断开MQTT连接时发生异常: " + std::string(e.what()));
    }
}

void MqttResultProtocol::mqtt_thread_func() {
    LOG_INFO("MQTT线程已启动");

    // 尝试连接到服务器
    if (!connect_to_server()) {
        LOG_ERROR("无法连接到MQTT服务器，MQTT线程退出");
        running_ = false;
        return;
    }

    // 保持连接并处理重连
    while (running_) {
        if (mqtt_client_) {
            MqttClientWrapper* wrapper = static_cast<MqttClientWrapper*>(mqtt_client_);
            if (!wrapper->client->is_connected()) {
                LOG_WARNING("MQTT连接丢失，尝试重连");
                client_count_ = 0;
                
                // 尝试重连
                if (connect_to_server()) {
                    LOG_INFO("MQTT重连成功");
                } else {
                    LOG_ERROR("MQTT重连失败，将在5秒后重试");
                    std::this_thread::sleep_for(std::chrono::seconds(5));
                }
            }
        }
        
        // 休眠一段时间再检查连接状态
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }

    LOG_INFO("MQTT线程已退出");
}

} // namespace protocols
} // namespace core
