#pragma once

#include "core/protocols/result_protocol.h"
#include "aivideocore_export.h"
#include <string>
#include <memory>
#include <thread>
#include <atomic>
#include <mutex>
#include <functional>

namespace core {
namespace protocols {

/**
 * @brief MQTT协议实现类
 */
class AIVIDEOCORE_API MqttResultProtocol : public IResultProtocol {
public:
    /**
     * @brief 构造函数
     */
    MqttResultProtocol();

    /**
     * @brief 析构函数
     */
    ~MqttResultProtocol() override;

    /**
     * @brief 启动协议服务
     * @param port MQTT服务端口，默认为1883
     * @return 是否成功启动
     */
    bool start(int port) override;

    /**
     * @brief 停止协议服务
     */
    void stop() override;

    /**
     * @brief 发送结果数据
     * @param json_str JSON格式的结果数据
     * @return 是否成功发送
     */
    bool send_result(const std::string& json_str) override;

    /**
     * @brief 获取连接客户端数量
     * @return 连接客户端数量
     */
    int get_client_count() const override;

    /**
     * @brief 获取服务端口
     * @return 服务端口
     */
    int get_port() const override;

    /**
     * @brief 获取协议名称
     * @return 协议名称
     */
    std::string get_protocol_name() const override;

    /**
     * @brief 获取协议状态
     * @return 协议是否正在运行
     */
    bool is_running() const override;

    /**
     * @brief 设置新连接回调函数
     * @param callback 新连接回调函数
     */
    void set_new_connection_callback(std::function<void(void*)> callback) override;

    /**
     * @brief 设置MQTT服务器地址
     * @param server_address MQTT服务器地址，格式为 "tcp://hostname:port"
     */
    void set_server_address(const std::string& server_address);

    /**
     * @brief 设置MQTT主题
     * @param topic 发布结果的主题
     */
    void set_topic(const std::string& topic);

    /**
     * @brief 设置MQTT客户端ID
     * @param client_id 客户端ID
     */
    void set_client_id(const std::string& client_id);

    /**
     * @brief 设置MQTT用户名和密码
     * @param username 用户名
     * @param password 密码
     */
    void set_credentials(const std::string& username, const std::string& password);

    /**
     * @brief 设置QoS级别
     * @param qos QoS级别 (0, 1, 2)
     */
    void set_qos(int qos);

    /**
     * @brief 获取MQTT服务器地址
     * @return MQTT服务器地址
     */
    std::string get_server_address() const;

    /**
     * @brief 获取MQTT主题
     * @return MQTT主题
     */
    std::string get_topic() const;

    /**
     * @brief 获取MQTT客户端ID
     * @return 客户端ID
     */
    std::string get_client_id() const;

    /**
     * @brief 获取QoS级别
     * @return QoS级别
     */
    int get_qos() const;

private:
    std::atomic<bool> running_;
    std::atomic<int> client_count_;
    int port_;
    std::string server_address_;
    std::string topic_;
    std::string client_id_;
    std::string username_;
    std::string password_;
    int qos_;
    
    std::function<void(void*)> new_connection_callback_;
    mutable std::mutex config_mutex_;
    
    // MQTT客户端相关
    void* mqtt_client_;  // 使用void*避免头文件依赖
    std::thread mqtt_thread_;
    
    /**
     * @brief MQTT连接线程函数
     */
    void mqtt_thread_func();
    
    /**
     * @brief 初始化MQTT客户端
     * @return 是否成功初始化
     */
    bool initialize_mqtt_client();
    
    /**
     * @brief 清理MQTT客户端
     */
    void cleanup_mqtt_client();
    
    /**
     * @brief 连接到MQTT服务器
     * @return 是否成功连接
     */
    bool connect_to_server();
    
    /**
     * @brief 断开MQTT连接
     */
    void disconnect_from_server();
};

} // namespace protocols
} // namespace core
