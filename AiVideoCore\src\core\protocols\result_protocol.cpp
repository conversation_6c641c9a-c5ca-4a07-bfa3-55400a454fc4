#include "core/protocols/result_protocol.h"
#include "core/protocols/tcp_result_protocol.h"
#include "core/protocols/modbus_result_protocol.h"
#include "core/protocols/mqtt_result_protocol.h"
#include "utils/log_manager.h"

namespace core {
namespace protocols {

std::shared_ptr<IResultProtocol> create_protocol(ProtocolType type) {
    switch (type) {
        case ProtocolType::TCP:
            LOG_INFO("创建TCP协议实例");
            return std::make_shared<TcpResultProtocol>();
        case ProtocolType::MODBUS:
            LOG_INFO("创建Modbus协议实例");
            return std::make_shared<ModbusResultProtocol>();
        case ProtocolType::MQTT:
            LOG_INFO("创建MQTT协议实例");
            return std::make_shared<MqttResultProtocol>();
        case ProtocolType::CUSTOM:
            LOG_WARNING("自定义协议尚未实现");
            return nullptr;
        default:
            LOG_ERROR("未知的协议类型");
            return nullptr;
    }
}

} // namespace protocols
} // namespace core
