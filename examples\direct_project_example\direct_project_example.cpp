/**
 * @file mqtt_example.cpp
 * @brief MQTT协议使用示例
 * 
 * 本示例演示如何使用VideoResultStorageServer的MQTT协议功能
 */

 #include <iostream>
 #include <thread>
 #include <chrono>
 #include "core/video_processing_core.h"
 #include "core/protocols/result_protocol.h"
 #include "utils/log_manager.h"
 #include <windows.h>
 
 int main() {
    #ifdef _WIN32
    SetConsoleOutputCP(CP_UTF8);
    #endif
     try {
         // 初始化日志管理器
         utils::LogManager::get_instance().initialize("mqtt", utils::LogLevel::Debug, utils::LogLevel::Debug, "logs");

         
         LOG_INFO("=== MQTT协议示例开始 ===");
         
         // 创建VideoProcessingCore实例
         core::VideoProcessingCore processor;
         
         // 启动MQTT结果存储服务
         std::string storage_path = "mqtt_results";
         int mqtt_port = 1883;  // 标准MQTT端口
         
         LOG_INFO("启动MQTT结果存储服务...");
         bool success = processor.start_result_storage_server(
             storage_path,
             core::VideoResultStorageServer::StorageMode::IMMEDIATE,
             mqtt_port,
             5000,  // 刷新间隔
             core::protocols::ProtocolType::MQTT
         );
         
         if (!success) {
             LOG_ERROR("启动MQTT结果存储服务失败");
             return -1;
         }
         
         LOG_INFO("MQTT结果存储服务启动成功");
         
         // 获取结果存储服务器实例并配置MQTT参数
         auto storage_server = processor.get_result_storage_server();
         if (storage_server) {
             // 配置MQTT服务器地址
             storage_server->set_mqtt_server_address("tcp://localhost:1883");
             
             // 配置MQTT主题
             storage_server->set_mqtt_topic("aivideo/detection_results");
             
             // 配置客户端ID
             storage_server->set_mqtt_client_id("aivideo_publisher_001");
             
             // 配置QoS级别
             storage_server->set_mqtt_qos(1);  // At least once delivery
             
             // 如果需要认证，可以设置用户名和密码
             // storage_server->set_mqtt_credentials("username", "password");
             
             LOG_INFO("MQTT配置完成:");
             LOG_INFO("  服务器地址: " + storage_server->get_mqtt_server_address());
             LOG_INFO("  主题: " + storage_server->get_mqtt_topic());
             LOG_INFO("  客户端ID: " + storage_server->get_mqtt_client_id());
             LOG_INFO("  QoS级别: " + std::to_string(storage_server->get_mqtt_qos()));
         }
         
         // 模拟发送一些检测结果
         LOG_INFO("开始发送模拟检测结果...");
         
         for (int i = 0; i < 10; ++i) {
             // 创建模拟的检测结果
             ai::FrameResult result;
             result.frame_id = i;
             result.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
                 std::chrono::system_clock::now().time_since_epoch()).count();
             
             // 添加一些模拟的检测结果
             result.task_type = "detection";
             result.ext_info = "person detected with confidence 0.85";
 
             // 发送结果到存储服务器
             if (storage_server) {
                 storage_server->add_result(result);
             }
             
             LOG_INFO("发送第 " + std::to_string(i + 1) + " 个检测结果");
             
             // 等待1秒
             std::this_thread::sleep_for(std::chrono::seconds(1));
         }
         
         LOG_INFO("所有检测结果发送完成");
         
         // 等待一段时间确保所有消息都被发送
         LOG_INFO("等待消息发送完成...");
         std::this_thread::sleep_for(std::chrono::seconds(3));
         
         // 显示服务状态
         if (storage_server) {
             LOG_INFO("服务状态:");
             LOG_INFO("  运行状态: " + std::string(storage_server->is_running() ? "运行中" : "已停止"));
             LOG_INFO("  连接数: " + std::to_string(storage_server->get_client_count()));
             LOG_INFO("  协议类型: " + storage_server->get_protocol_name());
         }
         
         // 停止服务
         LOG_INFO("停止MQTT结果存储服务...");
         processor.stop_result_storage_server();
         
         LOG_INFO("=== MQTT协议示例结束 ===");
         
     } catch (const std::exception& e) {
         LOG_ERROR("示例运行时发生异常: " + std::string(e.what()));
         return -1;
     } catch (...) {
         LOG_ERROR("示例运行时发生未知异常");
         return -1;
     }
     
     return 0;
 }
 
 /**
  * 使用说明:
  * 
  * 1. 确保已安装MQTT服务器（如Mosquitto）
  *    - Windows: 下载并安装Mosquitto
  *    - Linux: sudo apt-get install mosquitto mosquitto-clients
  *    - macOS: brew install mosquitto
  * 
  * 2. 启动MQTT服务器
  *    mosquitto -p 1883
  * 
  * 3. 编译并运行此示例
  * 
  * 4. 可以使用MQTT客户端订阅消息来查看结果:
  *    mosquitto_sub -h localhost -p 1883 -t "aivideo/detection_results"
  * 
  * 5. 或者使用MQTT客户端工具（如MQTT Explorer）连接到服务器查看消息
  * 
  * 注意事项:
  * - 如果没有安装Paho MQTT C++库，MQTT功能将被禁用
  * - 确保MQTT服务器正在运行，否则连接会失败
  * - 可以根据需要调整MQTT服务器地址、端口和主题
  */
 