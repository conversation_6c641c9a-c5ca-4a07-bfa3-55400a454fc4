# 添加直接项目处理示例
add_executable(direct_project_example direct_project_example.cpp)
target_link_libraries(direct_project_example PRIVATE AiVideoCore)

# 安装示例
install(TARGETS direct_project_example
        RUNTIME DESTINATION release
        LIBRARY DESTINATION lib
        ARCHIVE DESTINATION lib)

# 确保插件目录存在
install(CODE "file(MAKE_DIRECTORY \${CMAKE_INSTALL_PREFIX}/release/plugins/task)")

# 复制依赖的DLL文件到输出目录
if(WIN32)
    add_custom_command(TARGET direct_project_example POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E copy_if_different
            $<TARGET_FILE:AiVideoCore>
            $<TARGET_FILE_DIR:direct_project_example>
    )
endif()
