

# 收集其他源文件
file(GLOB_RECURSE VIDEO_AI_SOURCES
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*.cpp"
    "${CMAKE_CURRENT_SOURCE_DIR}/src/*/*/*.cpp"
)

# 收集头文件
file(GLOB_RECURSE VIDEO_AI_HEADERS
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*.h"
    "${CMAKE_CURRENT_SOURCE_DIR}/include/*/*/*.h"
)

# 设置 CMake 的编码
if(MSVC)
    # 为 MSVC 编译器设置 UTF-8 编码
    add_compile_options(
        /utf-8                   # 强制使用 UTF-8
        /wd4819                  # 禁用 code page 警告
        /DWIN32_LEAN_AND_MEAN   # 减少 Windows 头文件包含
    )

    # 添加 Unicode 定义
    add_compile_definitions(
        _UNICODE
        UNICODE
        NOMINMAX                 # 避免 Windows 宏与 STL 冲突
    )
endif()

# 确保源文件使用 UTF-8 编码
if(CMAKE_CXX_COMPILER_ID MATCHES "GNU|Clang")
    add_compile_options(-finput-charset=UTF-8)
endif()

if(MSVC)
    add_compile_definitions(
        _SILENCE_CXX17_CODECVT_HEADER_DEPRECATION_WARNING
        HAVE_SNPRINTF
        _CRT_SECURE_NO_WARNINGS
    )
endif()

# Ensure Python definitions are set correctly
add_compile_definitions(
    PYTHON_EXECUTABLE="${Python3_EXECUTABLE}"
    PYTHON_INCLUDE_DIR="${Python3_INCLUDE_DIRS}"
    PYTHON_LIBRARY="${Python3_LIBRARIES}"
)

# 创建AiVideoCore库
add_library(AiVideoCore SHARED ${VIDEO_AI_SOURCES} ${VIDEO_AI_HEADERS})
target_include_directories(
    AiVideoCore
    PUBLIC
    $<BUILD_INTERFACE:${CMAKE_CURRENT_SOURCE_DIR}/include>
    $<INSTALL_INTERFACE:include>
)

# 定义导出宏
target_compile_definitions(AiVideoCore
    PRIVATE AIVIDEOCORE_EXPORTS
    PUBLIC AIVIDEOCORE_SHARED
)

set(_boost_libs
    # Boost::boost
    AIDIBOOST::atomic
    AIDIBOOST::chrono
    AIDIBOOST::date_time
    AIDIBOOST::filesystem
    AIDIBOOST::log
    AIDIBOOST::log_setup
    AIDIBOOST::regex
    AIDIBOOST::system
    AIDIBOOST::thread
)


target_link_libraries(AiVideoCore PUBLIC
    AIDIOPENCV
    VisionFlow
    AIDIJSONCPP
    pybind11::embed
    Python3::Python
    ${_boost_libs}
)

# 链接MQTT库（如果可用）
if(MQTT_FOUND)
    find_package(PahoMqttCpp CONFIG REQUIRED)
    target_link_libraries(AiVideoCore PRIVATE PahoMqttCpp::paho-mqttpp3 PahoMqttCpp::paho-mqttpp3-shared)
    message(STATUS "MQTT support enabled for AiVideoCore")
else()
    message(STATUS "MQTT support disabled for AiVideoCore")
endif()

target_compile_definitions(AiVideoCore PRIVATE VFLOW_ENABLE_OPENCV NON_BLOCKING_MODE)

# 安装目标
install(
    TARGETS AiVideoCore
    EXPORT AiVideoCoreTargets
    ARCHIVE DESTINATION lib
    RUNTIME DESTINATION release
    LIBRARY DESTINATION lib
)

# 安装头文件
install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/include/
    DESTINATION include
    FILES_MATCHING PATTERN "*.h"
)

# 创建脚本目录
install(
    DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/scripts/
    DESTINATION release/scripts
    FILES_MATCHING PATTERN "*.py"
)


















