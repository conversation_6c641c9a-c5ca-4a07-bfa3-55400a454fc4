# MQTT协议支持实现总结

本文档总结了为AiVideoCore的VideoResultStorageServer添加MQTT协议支持的完整实现。

## 实现概述

VideoResultStorageServer现在支持通过MQTT协议发布视频处理结果，与现有的TCP和Modbus协议并列。MQTT协议特别适合物联网应用场景，提供轻量级、可靠的消息传输。

## 新增文件

### 1. 核心实现文件

- **`include/core/protocols/mqtt_result_protocol.h`** - MQTT协议头文件
  - 定义了MqttResultProtocol类
  - 实现IResultProtocol接口
  - 提供MQTT特定的配置方法

- **`src/core/protocols/mqtt_result_protocol.cpp`** - MQTT协议实现文件
  - 完整的MQTT客户端功能
  - 支持连接、发布、重连等功能
  - 使用Paho MQTT C++库

### 2. 示例和文档

- **`examples/mqtt_example.cpp`** - MQTT使用示例
  - 演示如何配置和使用MQTT协议
  - 包含完整的使用流程

- **`docs/MQTT_SETUP.md`** - MQTT设置指南
  - 详细的安装和配置说明
  - 故障排除指南

## 修改的文件

### 1. 构建系统

- **`cmake/find_mqtt.cmake`** - 新增MQTT查找脚本
  - 智能查找：优先使用系统已安装的MQTT库
  - 自动下载：使用FetchContent自动下载和编译Paho MQTT C++库
  - 条件编译支持（MQTT_ENABLED宏）
  - 提供target_link_mqtt函数简化链接

- **`CMakeLists.txt`** - 根目录CMakeLists.txt
  - 添加了find_mqtt.cmake的包含

- **`AiVideoCore/CMakeLists.txt`** - 子目录CMakeLists.txt
  - 使用target_link_mqtt函数链接MQTT库
  - 简化的条件编译逻辑

### 2. 协议工厂

- **`src/core/protocols/result_protocol.cpp`**
  - 添加了MQTT协议的创建逻辑
  - 更新了协议工厂函数

### 3. VideoResultStorageServer

- **`include/core/video_result_storage_server.h`**
  - 添加了MQTT配置方法声明
  - 包括服务器地址、主题、客户端ID、认证、QoS等配置

- **`src/core/video_result_storage_server.cpp`**
  - 实现了所有MQTT配置方法
  - 添加了MQTT协议头文件包含

## 功能特性

### 1. 核心功能

- **协议支持**: 完整的MQTT客户端功能
- **消息发布**: 将FrameResult转换为JSON并发布到MQTT主题
- **连接管理**: 自动连接、重连机制
- **错误处理**: 完善的异常处理和日志记录

### 2. 配置选项

- **服务器地址**: 支持tcp://hostname:port格式
- **主题设置**: 可自定义发布主题
- **客户端ID**: 可设置唯一的客户端标识
- **认证支持**: 用户名和密码认证
- **QoS级别**: 支持0、1、2三种QoS级别

### 3. 安全特性

- **条件编译**: 如果没有MQTT库，功能会被禁用
- **错误恢复**: 连接失败时的自动重试机制
- **资源管理**: 正确的资源清理和内存管理

## 使用方法

### 1. 基本使用

```cpp
// 创建处理器
core::VideoProcessingCore processor;

// 启动MQTT结果存储服务
bool success = processor.start_result_storage_server(
    "results",                                          // 存储路径
    core::VideoResultStorageServer::StorageMode::IMMEDIATE,
    1883,                                              // MQTT端口
    5000,                                              // 刷新间隔
    core::protocols::ProtocolType::MQTT                // 协议类型
);

// 配置MQTT参数
auto storage_server = processor.get_result_storage_server();
storage_server->set_mqtt_server_address("tcp://localhost:1883");
storage_server->set_mqtt_topic("aivideo/results");
storage_server->set_mqtt_client_id("aivideo_client");
storage_server->set_mqtt_qos(1);
```

### 2. 高级配置

```cpp
// 设置认证
storage_server->set_mqtt_credentials("username", "password");

// 设置自定义服务器
storage_server->set_mqtt_server_address("tcp://*************:1883");

// 设置自定义主题
storage_server->set_mqtt_topic("factory/line1/detection_results");
```

## 依赖要求

### 1. 自动管理的依赖

- **Paho MQTT C++库**: 通过FetchContent自动下载和编译
- **Paho MQTT C库**: 作为C++库的依赖自动处理
- **网络连接**: 首次编译时需要下载源码

### 2. 运行时依赖

- **MQTT服务器**: 如Mosquitto、EMQ X等

## 编译配置

### 1. 智能依赖管理

CMake使用智能策略处理MQTT依赖：
1. **优先查找**: 首先尝试查找系统已安装的Paho MQTT C++库
2. **自动下载**: 如果未找到，使用FetchContent自动下载和编译
3. **版本控制**: 使用固定版本确保一致性（C库v1.3.13，C++库v1.3.2）
4. **编译优化**: 自动配置静态链接、禁用测试等选项

### 2. 条件编译

- 所有MQTT相关代码都使用`#ifdef MQTT_ENABLED`保护
- 如果MQTT库不可用，功能会被完全禁用
- 确保在没有MQTT库时也能正常编译和运行

### 3. 构建优势

- **零配置**: 开发者无需手动安装MQTT库
- **一致性**: 所有开发者使用相同版本的MQTT库
- **可靠性**: 避免了版本冲突和依赖问题
- **灵活性**: 支持使用系统已安装的库或自动下载

## 消息格式

MQTT消息采用JSON格式，与其他协议保持一致：

```json
{
    "frame_id": 123,
    "timestamp": 1640995200000,
    "task_type": "detection",
    "total_count": 5,
    "ext_info": "additional information",
    "detections": [
        {
            "track_id": 1,
            "class": "person",
            "score": 0.85,
            "state": 1,
            "duration": 2.5,
            "bbox": {
                "x": 100,
                "y": 100,
                "width": 80,
                "height": 120
            }
        }
    ],
    "class_counts": {
        "person": 3,
        "vehicle": 2
    }
}
```

## 性能考虑

### 1. 网络延迟

MQTT是网络协议，会受到网络条件影响。建议：
- 在本地网络中使用以获得最佳性能
- 根据网络条件调整QoS级别

### 2. 消息频率

高频率的消息发送可能影响性能：
- 考虑使用批量发送
- 根据应用需求调整发送频率

### 3. QoS级别选择

- **QoS 0**: 最快，但可能丢失消息
- **QoS 1**: 平衡性能和可靠性
- **QoS 2**: 最可靠，但最慢

## 故障排除

### 1. 编译问题

- 确保安装了Paho MQTT C++库
- 检查CMake能否找到库文件

### 2. 运行时问题

- 检查MQTT服务器是否运行
- 验证网络连接和防火墙设置
- 查看日志输出获取详细错误信息

## 未来扩展

### 1. 可能的改进

- 支持SSL/TLS加密连接
- 添加消息持久化选项
- 支持订阅功能（双向通信）
- 添加更多的连接选项配置

### 2. 集成建议

- 与现有的监控系统集成
- 支持多个MQTT服务器的负载均衡
- 添加消息压缩功能

## 总结

MQTT协议支持的添加为AiVideoCore提供了更灵活的结果发布选项，特别适合物联网和分布式系统场景。实现遵循了现有的架构模式，保持了代码的一致性和可维护性。
